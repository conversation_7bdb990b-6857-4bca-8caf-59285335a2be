package com.example.demo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

/**
 * 数据库配置类
 */
@Configuration
public class DatabaseConfig {

    /**
     * 数据源配置已在application.properties中完成
     * Spring Boot会自动创建DataSource和JdbcTemplate
     * 此处仅作为备用方案，如果需要自定义数据源可以取消注释
     */
    /*
    @Bean
    public DataSource dataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl("**************************************");
        dataSource.setUsername("mcpuser");
        dataSource.setPassword("mcppass");
        return dataSource;
    }

    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
    */
} 