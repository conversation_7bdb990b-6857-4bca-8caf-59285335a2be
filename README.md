# 体检指标 MCP 服务

这是一个基于 Spring Boot 的体检指标 MCP 服务，使用 PostgreSQL 数据库存储体检数据。

## 项目介绍

该项目提供了符合 OpenAI Chat API 格式的接口，用于查询用户的体检指标数据。

## 功能特点

- 基于 Spring Boot 和 PostgreSQL 数据库
- 提供符合 OpenAI Chat API 格式的接口
- 支持通过用户姓名查询体检指标

## 数据库表结构

```sql
CREATE TABLE health_check (
    id               SERIAL PRIMARY KEY,
    person_name      VARCHAR(100) NOT NULL,
    gender           VARCHAR(10),
    age              INTEGER,
    check_date       DATE DEFAULT CURRENT_DATE,
    wbc_count        DOUBLE PRECISION,
    neutrophil_pct   DOUBLE PRECISION,
    lymphocyte_pct   DOUBLE PRECISION,
    monocyte_pct     DOUBLE PRECISION,
    neutrophil_count DOUBLE PRECISION
);
```

## 快速开始

### 1. 启动 PostgreSQL 数据库

```bash
docker run --name postgres-mcp \
  -e POSTGRES_USER=mcpuser \
  -e POSTGRES_PASSWORD=mcppass \
  -e POSTGRES_DB=mcpdb \
  -p 5432:5432 \
  -d postgres
```

### 2. 初始化数据库

连接到数据库并执行 `schema.sql` 和 `data.sql` 文件中的 SQL 语句。

```bash
# 进入容器
docker exec -it postgres-mcp psql -U mcpuser -d mcpdb

# 在 psql 中执行 SQL
\i /path/to/schema.sql
\i /path/to/data.sql
```

### 3. 启动应用

```bash
./mvnw spring-boot:run
```

## API 使用

### 查询体检指标

```
POST /v1/health-check/completions
```

请求体示例:

```json
{
  "person_name": "张三"
}
```

或者使用 messages 格式:

```json
{
  "messages": [
    {
      "role": "user",
      "content": "张三"
    }
  ]
}
```

响应示例:

```json
{
  "knowledge": "体检指标详情：\n- 姓名: 张三\n- 性别: 男\n- 年龄: 35岁\n- 检查日期: 2023-07-15\n\n血液检查指标：\n- 白细胞计数: 6.3 10^9/L\n- 中性粒细胞百分比: 62.4%\n- 淋巴细胞百分比: 33.1%\n- 单核细胞百分比: 4.5%\n- 中性粒细胞计数: 4.1 10^9/L\n"
}
```

## 配置

配置文件位于 `src/main/resources/application.properties`：

```properties
spring.datasource.url=**************************************
spring.datasource.username=mcpuser
spring.datasource.password=mcppass
spring.datasource.driver-class-name=org.postgresql.Driver
``` 