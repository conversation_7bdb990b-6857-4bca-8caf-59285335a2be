-- MySQL数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS qcx DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE qcx;

-- 创建体检指标表
DROP TABLE IF EXISTS health_check;

CREATE TABLE health_check (
    id               INT AUTO_INCREMENT PRIMARY KEY,
    person_name      VARCHAR(100) NOT NULL,
    gender           VARCHAR(10),
    age              INT,
    check_date       DATE DEFAULT (CURRENT_DATE),
    wbc_count        DOUBLE,
    neutrophil_pct   DOUBLE,
    lymphocyte_pct   DOUBLE,
    monocyte_pct     DOUBLE,
    neutrophil_count DOUBLE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入示例数据
INSERT INTO health_check (person_name, gender, age, check_date, wbc_count, neutrophil_pct, lymphocyte_pct, monocyte_pct, neutrophil_count)
VALUES ('张三', '男', 35, CURDATE(), 6.30, 62.4, 33.1, 4.5, 4.10);

INSERT INTO health_check (person_name, gender, age, check_date, wbc_count, neutrophil_pct, lymphocyte_pct, monocyte_pct, neutrophil_count)
VALUES ('李四', '女', 28, CURDATE(), 5.80, 58.2, 36.5, 5.3, 3.75);

INSERT INTO health_check (person_name, gender, age, check_date, wbc_count, neutrophil_pct, lymphocyte_pct, monocyte_pct, neutrophil_count)
VALUES ('王五', '男', 42, CURDATE(), 7.10, 65.8, 30.2, 4.0, 4.65);

-- 查看插入的数据
SELECT * FROM health_check;
