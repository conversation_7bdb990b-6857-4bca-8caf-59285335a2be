package com.example.demo.controller;

import com.example.demo.model.HealthCheck;
import com.example.demo.service.HealthCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 体检指标MCP控制器 - 提供符合OpenAI Chat API格式的接口
 * 用于将体检指标查询接口封装为MCP服务
 */
@RestController
@RequestMapping("/v1/health-check")
public class HealthCheckMcpController {

    private static final Logger logger = LoggerFactory.getLogger(HealthCheckMcpController.class);

    private final HealthCheckService healthCheckService;

    public HealthCheckMcpController(HealthCheckService healthCheckService) {
        this.healthCheckService = healthCheckService;
    }

    /**
     * 符合OpenAI Chat Completions API格式的MCP接口
     * 用于查询体检指标
     */
    @PostMapping("/completions")
    public ResponseEntity<Map<String, Object>> chatCompletions(@RequestBody Map<String, Object> body) {
        try {
            logger.info("接收到体检指标MCP请求: {}", body);
            
            // 优先从body顶层获取person_name
            String personName = (String) body.get("person_name");
            if (personName == null || personName.isEmpty()) {
                // 兼容原有messages结构
                personName = extractPersonNameFromMessages(body);
            }
            if (personName == null || personName.isEmpty()) {
                logger.warn("MCP请求中未找到有效的personName");
                return createErrorResponse("请提供有效的用户姓名");
            }
            
            // 调用体检指标查询服务
            logger.info("MCP服务开始查询体检指标: personName={}", personName);
            HealthCheck healthCheck = healthCheckService.getHealthCheckByPersonName(personName);
            
            if (healthCheck == null) {
                logger.warn("未找到用户体检指标: personName={}", personName);
                return createErrorResponse("未找到该用户的体检指标");
            }
            
            // 构建响应
            logger.info("MCP服务成功查询体检指标: personName={}", personName);
            return ResponseEntity.ok(createSuccessResponse(healthCheck));
            
        } catch (Exception e) {
            logger.error("MCP服务处理异常", e);
            return createErrorResponse("处理请求时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 从请求消息中提取personName
     */
    private String extractPersonNameFromMessages(Map<String, Object> body) {
        try {
            List<Map<String, String>> messages = (List<Map<String, String>>) body.get("messages");
            if (messages != null) {
                for (Map<String, String> msg : messages) {
                    if ("user".equals(msg.get("role"))) {
                        // 假设用户消息直接是personName
                        return msg.get("content").trim();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析消息异常", e);
        }
        return null;
    }
    
    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(HealthCheck healthCheck) {
        Map<String, Object> response = new HashMap<>();
        StringBuilder content = new StringBuilder();
        content.append("体检指标详情：\n");
        content.append("- 姓名: ").append(healthCheck.getPersonName()).append("\n");
        if (healthCheck.getGender() != null) {
            content.append("- 性别: ").append(healthCheck.getGender()).append("\n");
        }
        if (healthCheck.getAge() != null) {
            content.append("- 年龄: ").append(healthCheck.getAge()).append("岁\n");
        }
        if (healthCheck.getCheckDate() != null) {
            content.append("- 检查日期: ").append(healthCheck.getCheckDate()).append("\n");
        }
        
        // 体检指标
        content.append("\n血液检查指标：\n");
        if (healthCheck.getWbcCount() != null) {
            content.append("- 白细胞计数: ").append(healthCheck.getWbcCount()).append(" 10^9/L\n");
        }
        if (healthCheck.getNeutrophilPct() != null) {
            content.append("- 中性粒细胞百分比: ").append(healthCheck.getNeutrophilPct()).append("%\n");
        }
        if (healthCheck.getLymphocytePct() != null) {
            content.append("- 淋巴细胞百分比: ").append(healthCheck.getLymphocytePct()).append("%\n");
        }
        if (healthCheck.getMonocytePct() != null) {
            content.append("- 单核细胞百分比: ").append(healthCheck.getMonocytePct()).append("%\n");
        }
        if (healthCheck.getNeutrophilCount() != null) {
            content.append("- 中性粒细胞计数: ").append(healthCheck.getNeutrophilCount()).append(" 10^9/L\n");
        }
        
        // 只返回 knowledge 字段
        response.put("knowledge", content.toString());
        return response;
    }
    
    /**
     * 创建错误响应
     */
    private ResponseEntity<Map<String, Object>> createErrorResponse(String errorMessage) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", Map.of(
            "message", errorMessage,
            "type", "invalid_request_error",
            "code", "invalid_person_name"
        ));
        return ResponseEntity.badRequest().body(errorResponse);
    }
} 