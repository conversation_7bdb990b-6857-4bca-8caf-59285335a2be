spring.application.name=health-check-mcp


spring.datasource.url=********************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql


logging.level.root=INFO
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG
